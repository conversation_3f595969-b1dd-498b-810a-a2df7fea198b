import { vi, describe, it, expect, beforeEach } from 'vitest';
import { CommentThreadCacheService, ThreadComment } from '../../../../src/slack/services/comment-thread-cache.service';

describe('CommentThreadCacheService - Performance Demo', () => {
  let service: CommentThreadCacheService;
  let mockThenaPlatformApiProvider: any;
  let mockLogger: any;
  let apiCallCount: number;

  const mockInstallation = { id: 'installation-123' };
  const parentCommentId = 'parent-comment-123';

  const mockThreadComments: ThreadComment[] = [
    {
      id: 'comment-1',
      metadata: {
        external_sinks: {
          slack: { ts: '1000.001' }
        }
      }
    },
    {
      id: 'comment-2',
      metadata: {
        external_sinks: {
          slack: { ts: '1000.002' }
        }
      }
    },
    {
      id: 'comment-3',
      metadata: {
        external_sinks: {
          slack: { ts: '1000.003' }
        }
      }
    }
  ];

  beforeEach(() => {
    apiCallCount = 0;

    mockLogger = {
      debug: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
      log: vi.fn(),
    };

    mockThenaPlatformApiProvider = {
      getCommentThreads: vi.fn().mockImplementation(async () => {
        apiCallCount++;
        console.log(`🔥 API Call #${apiCallCount} - getCommentThreads()`);
        return mockThreadComments;
      }),
    };

    // Create service instance directly (bypassing NestJS DI complexity)
    service = new CommentThreadCacheService(mockLogger, mockThenaPlatformApiProvider);
  });

  it('🚀 PERFORMANCE DEMONSTRATION: O(n²) to O(1) improvement', async () => {
    console.log('\n=== PERFORMANCE IMPROVEMENT DEMONSTRATION ===');

    // Simulate processing multiple reactions on thread replies (like in ticket creation)
    const slackTimestamps = ['1000.001', '1000.002', '1000.003', '1000.001', '1000.002'];

    console.log(`\n📊 Scenario: Processing ${slackTimestamps.length} comment lookups`);
    console.log(`   Thread has ${mockThreadComments.length} comments`);
    console.log('   Simulating: ticket creation with reactions on each reply\n');

    // Reset API call counter
    apiCallCount = 0;

    // Process all timestamps (simulating multiple reaction events during ticket creation)
    const results: (string | null)[] = [];
    for (let i = 0; i < slackTimestamps.length; i++) {
      const ts = slackTimestamps[i];
      console.log(`   Processing lookup ${i + 1}: Slack TS ${ts}`);

      const commentId = await service.getCommentIdBySlackTs(
        mockInstallation as any,
        parentCommentId,
        ts
      );
      results.push(commentId);

      console.log(`   → Found comment ID: ${commentId}`);
    }

    // Verify results are correct
    expect(results).toEqual(['comment-1', 'comment-2', 'comment-3', 'comment-1', 'comment-2']);

    // Most importantly: API was called only ONCE despite 5 lookups
    expect(apiCallCount).toBe(1);

    // Log the performance improvement
    const stats = service.getCacheStats();

    console.log('\n📈 PERFORMANCE RESULTS:');
    console.log(`   ✅ Total API calls made: ${apiCallCount}`);
    console.log(`   ✅ Total comment lookups: ${slackTimestamps.length}`);
    console.log(`   ✅ Cache stats: ${JSON.stringify(stats)}`);
    console.log('\n💡 PERFORMANCE COMPARISON:');
    console.log(`   ❌ WITHOUT cache: ${slackTimestamps.length} API calls × ${mockThreadComments.length} comments = ${slackTimestamps.length * mockThreadComments.length} operations`);
    console.log(`   ✅ WITH cache: ${apiCallCount} API call + ${slackTimestamps.length} cache lookups = ${apiCallCount + slackTimestamps.length} operations`);

    const improvement = Math.round((1 - (apiCallCount + slackTimestamps.length) / (slackTimestamps.length * mockThreadComments.length)) * 100);
    console.log(`   🚀 Performance improvement: ${improvement}% reduction in operations!`);
    console.log('\n=== DEMONSTRATION COMPLETE ===\n');
  });

  it('should demonstrate cache hit/miss behavior', async () => {
    console.log('\n=== CACHE BEHAVIOR DEMONSTRATION ===');

    // First call - cache miss
    console.log('\n1️⃣ First call (cache miss):');
    const result1 = await service.getCommentThreads(mockInstallation as any, parentCommentId);
    expect(result1).toEqual(mockThreadComments);
    expect(apiCallCount).toBe(1);
    console.log(`   API calls so far: ${apiCallCount}`);

    // Second call - cache hit
    console.log('\n2️⃣ Second call (cache hit):');
    const result2 = await service.getCommentThreads(mockInstallation as any, parentCommentId);
    expect(result2).toEqual(mockThreadComments);
    expect(apiCallCount).toBe(1); // Still 1!
    console.log(`   API calls so far: ${apiCallCount} (no additional call!)`);

    // Clear cache and call again - cache miss
    console.log('\n3️⃣ After cache clear (cache miss):');
    service.clearCache();
    const result3 = await service.getCommentThreads(mockInstallation as any, parentCommentId);
    expect(result3).toEqual(mockThreadComments);
    expect(apiCallCount).toBe(2);
    console.log(`   API calls so far: ${apiCallCount} (new call after cache clear)`);

    console.log('\n=== CACHE DEMONSTRATION COMPLETE ===\n');
  });
});
