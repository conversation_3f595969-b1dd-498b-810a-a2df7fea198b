import { Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { EmittableSlackEvents } from '../../../../external/provider/constants/platform-events.constants';
import { ThenaPlatformApiProvider } from '../../../../external/provider/thena-platform-api.provider';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../../../utils';
import { SlackEvent } from '../../../decorators';
import { SlackEventMap } from '../../interface';
import { BaseSlackEventHandler } from '../../interface';
import { SlackMessagesRepository } from '../../../../database/entities/slack-messages/repositories/slack-messages.repository';
import { GroupedSlackMessagesRepository } from '../../../../database/entities/slack-messages/repositories/grouped-slack-messages.repository';
import { ChannelsRepository } from '../../../../database/entities/channels/repositories';
import { CommentMetadata } from '../../../../platform/type-system/events';
import { Installations } from '../../../../database/entities';
import { SlackWebAPIService } from '../../../providers/slack-apis/slack-apis.service';
import { ThenaAppsPlatformApiProvider } from '../../../../external/provider/thena-apps-platform-api.provider';

@Injectable()
@SlackEvent('reaction_removed')
export class SlackReactionRemovedHandler extends BaseSlackEventHandler<'reaction_removed'> {
  eventType = 'reaction_removed' as const;

  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,
    private readonly slackMessagesRepository: SlackMessagesRepository,
    private readonly groupedSlackMessagesRepository: GroupedSlackMessagesRepository,
    private readonly channelRepository: ChannelsRepository,
    private readonly thenaPlatformApiProvider: ThenaPlatformApiProvider,
    private readonly thenaAppsPlatformApiProvider: ThenaAppsPlatformApiProvider,
    private readonly slackApiProvider: SlackWebAPIService ,
  ) {
    super();
  }

  canHandle(event: SlackEventMap['reaction_removed']): boolean {
    return event.event.type === 'reaction_removed';
  }

  async handle(e: SlackEventMap['reaction_removed']): Promise<void> {
    try {
      // Check if the channel property is present
      if (
        !('user' in e.event) ||
        !('reaction' in e.event) ||
        !this.canHandle(e)
      ) {
        throw new Error(
          'Invalid event received in the `reaction_removed` handler',
        );
      }

      const { context, event } = e;
      const { installation } = context;

      // Post the event to the platform
      await this.thenaAppsPlatformApiProvider.postEventsToPlatform(
        installation.organization,
        {
          ...event,
          type: EmittableSlackEvents.REACTION_REMOVED,
        },
      );

      const userInfo = await this.slackApiProvider.getUserInfo(installation.botToken, { user: event.user });


      // Call the removeReaction API for the specific comment
      try {
        const { item } = event;
        if (item.type !== 'message') {
          throw new Error('Reaction was not removed from a message');
        }

        // Get the slack channel
        const slackChannel = await this.channelRepository.findByCondition({
          where: { channelId: item.channel, installation: { id: installation.id } },
        });

        if (!slackChannel) {
          throw new Error(`Channel not found for ${installation.teamId}`);
        }

        // Get the related slack message
        const commentId = await this.getPlatformCommentId(installation, slackChannel.id, item.ts);

        // If no platform comment ID is found, skip processing
        if (!commentId) {
          this.logger.debug(
            `No platform comment found for Slack message ${item.ts}, skipping reaction removal`,
          );
          return;
        }

        await this.thenaPlatformApiProvider.removeReaction(
          installation,
          commentId,
          event.reaction,
          userInfo.user.profile.email,
        );
        this.logger.debug(
          `Successfully removed reaction ${event.reaction} from comment ${commentId}`,
        );
      } catch (reactionError) {
        this.logger.error(
          `Error removing reaction from comment: ${reactionError instanceof Error ? reactionError.message : 'Unknown error'}`,
        );
        // Don't rethrow, just log the error - this is a secondary operation
      }
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          `[SlackReactionRemovedHandler] Failed to handle 'reaction_removed' event for team ${e.context.installation.teamId} at ${e.event.event_ts}`,
          error.stack,
        );
      } else {
        console.error(
          `[SlackReactionRemovedHandler] Failed to handle 'reaction_removed' event for team ${e.context.installation.teamId} at ${e.event.event_ts}`,
          error,
        );
      }
    }
  }

  /**
   * Get the platform comment ID
   * @param installation Installation
   * @param channelId Channel ID
   * @param ts Message timestamp
   * @returns Platform comment ID or null if not found
   */
  private async getPlatformCommentId(
    installation: Installations,
    channelId: string,
    ts: string,
  ): Promise<string | null> {
    const commonWhereClause = {
      channel: { id: channelId },
      installation: { id: installation.id },
    };

    // Check if it's a direct message or threaded message
    const slackMessage = await this.slackMessagesRepository.findByCondition({
      where: [
        { ...commonWhereClause, slackMessageTs: ts },
        { ...commonWhereClause, slackMessageThreadTs: ts },
      ],
    });

    // If a message is found
    if (slackMessage) {
      // If it's the main message
      if (ts === slackMessage.slackMessageTs) {
        if (!slackMessage.platformCommentId) {
          this.logger.debug(
            `No platform comment ID found for the message: ${ts}, skipping reaction removal`,
          );
          return null;
        }
        return slackMessage.platformCommentId;
      }

      // It's a reply in a thread
      if (!slackMessage.platformCommentId) {
        this.logger.debug(
          `No parent platform comment ID found for the thread: ${ts}, skipping reaction removal`,
        );
        return null;
      }

      // Get thread comments
      const threads = await this.thenaPlatformApiProvider.getCommentThreads(
        installation,
        slackMessage.platformCommentId,
      );

      // Find the thread comment that matches the ts
      const commentFound = threads.find((th) => {
        const thMeta = th.metadata as CommentMetadata;
        const slackTs = thMeta?.external_sinks?.slack?.ts;
        return slackTs === ts;
      });

      if (!commentFound) {
        this.logger.debug(
          `No thread comment found for ts: ${ts}, skipping reaction removal`,
        );
        return null;
      }

      return commentFound.id;
    }

    // Check if it's a grouped message
    const groupedMessage = await this.groupedSlackMessagesRepository.findByCondition({
      where: {
        slackMessageTs: ts,
        channel: { id: channelId },
      },
    });

    if (groupedMessage) {
      if (!groupedMessage.parentCommentId) {
        this.logger.debug(
          `No parent comment ID found for grouped message: ${ts}, skipping reaction removal`,
        );
        return null;
      }
      return groupedMessage.parentCommentId;
    }

    this.logger.debug(
      `No related slack message found for ts: ${ts}, skipping reaction removal`,
    );
    return null;
  }
}
