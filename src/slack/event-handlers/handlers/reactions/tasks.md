1. []
In src/slack/services/ticket-creation-helper.service.ts around lines 826 to 833,
the code creates a new RegExp instance inside the loop for every message, which
is inefficient. To fix this, remove the creation of a new RegExp inside the loop
and instead reset the lastIndex property of the existing mentionRegex to 0
before starting the exec calls. This reuses the compiled RegExp and avoids
repeated compilation.

2. []
Mention-parsing regex misses the most common <@U12345> format

/<@([^|]+)\|([^>]+)>/g matches only the legacy labelled form (<@U12345|john>).
Standard Slack messages nearly always use the unlabeled format (<@U12345>), so the preload step silently ignores the vast majority of mentions, undermining the whole optimisation.

A minimal fix is to make the |… segment optional:

-const mentionRegex = /<@([^|]+)\|([^>]+)>/g;
+// capture user-id, ignore optional display part
+const mentionRegex = /<@([A-Z0-9]+)(?:\|[^>]+)?>/g;
This still captures the user-id in match[1], so the downstream logic remains intact.

📝 Committable suggestion
‼️ IMPORTANT
Carefully review the code before committing. Ensure that it accurately replaces the highlighted code, contains no missing lines, and has no issues with indentation. Thoroughly test & benchmark the code to ensure it meets the requirements.

Suggested change
    const mentionRegex = /<@([^|]+)\|([^>]+)>/g;
    const userIdsSet = new Set<string>();
    // Extract all user IDs from message mentions
    for (const message of messages) {
      if (!message.text) {
        continue;
      }
      // Find all user mentions in the message
      let match: RegExpExecArray | null;
      const regex = new RegExp(mentionRegex);
      while (true) {
        match = regex.exec(message.text);
        if (match === null) {
          break;
        }
        const userId = match[1]; // Use user ID instead of display name
        if (userId) {
          userIdsSet.add(userId);
        }
    // capture user-id, ignore optional display part
    const mentionRegex = /<@([A-Z0-9]+)(?:\|[^>]+)?>/g;
    const userIdsSet = new Set<string>();
    // Extract all user IDs from message mentions
    for (const message of messages) {
      if (!message.text) {
        continue;
      }
      // Find all user mentions in the message
      let match: RegExpExecArray | null;
      const regex = new RegExp(mentionRegex);
      while (true) {
        match = regex.exec(message.text);
        if (match === null) {
          break;
        }
        const userId = match[1]; // Use user ID instead of display name
        if (userId) {
          userIdsSet.add(userId);
        }
      }
    }
🤖 Prompt for AI Agents
In src/slack/services/ticket-creation-helper.service.ts around lines 816 to 838,
the mention-parsing regex only matches the legacy labeled Slack mention format
and misses the common unlabeled format. Update the regex to make the "|…"
segment optional so it matches both `<@U12345>` and `<@U12345|name>` formats.
This ensures user IDs are correctly extracted from all mentions without changing
downstream logic.
