import { Inject, Injectable } from '@nestjs/common';
import { SlackViewMiddlewareArgs } from '@slack/bolt';
import { PlatformTeams } from '../../../database/entities';
import { TeamChannelMapsRepository } from '../../../database/entities/mappings/repositories/team-channel-maps.repository';
import { TeamsRepository } from '../../../database/entities/teams';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../../utils';
import { CreateTicketsBlocksComposite } from '../../blocks/components';
import { CreateTicketCommand } from '../../commands/create-ticket.command';
import { SlackView } from '../../decorators';
import { DecoratedSlackViewMiddlewareArgs } from '../../event-handlers';
import { SlackActionHandler } from '../../interfaces';
import { EnrichedSlackArgsContext } from '../../services/slack-action-discovery.service';
import { TicketCreationHelper } from '../../services/ticket-creation-helper.service';

const LOG_SPAN = 'CreateTicketViewHandler';

@Injectable()
@SlackView(CreateTicketCommand.CREATE_TICKET_VIEW_CALLBACK_ID)
export class CreateTicketViewHandler implements SlackActionHandler {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,
    private readonly teamChannelMapsRepo: TeamChannelMapsRepository,
    private readonly platformTeamsRepo: TeamsRepository,
    private readonly ticketCreationHelper: TicketCreationHelper,
  ) {}

  async handle(args: DecoratedSlackViewMiddlewareArgs) {
    let context: EnrichedSlackArgsContext;
    if ('context' in args) {
      context = args.context as EnrichedSlackArgsContext;
    }

    this.logger.log(`${LOG_SPAN} Handling create ticket view submission`);

    const { body } = args;
    const { installation, organization, client } = context;

    try {
      if ('view' in args) {
        const view = args.view;

        const privateMetadata: {
          channelId: string;
          responseUrl: string;
          threadTs?: string;
          shouldLinkSlackMessage?: boolean;
        } = this.getPrivateMetadata(view);

        // Get the channel from the database
        const teamChannelMap = await this.teamChannelMapsRepo.findByCondition({
          where: {
            channel: { channelId: privateMetadata.channelId },
            installation: { id: installation.id },
            organization: { id: organization.id },
          },
          relations: { channel: true, platformTeam: true },
        });

        let platformTeam: PlatformTeams | null = null;

        // If the team channel map is not found, throw an error
        if (!teamChannelMap) {
          this.logger.debug(
            `${LOG_SPAN} Team channel map not found for channel ${privateMetadata.channelId}`,
          );

          const selectedTeamId =
            view.state.values?.team_select_block?.team_select?.selected_option
              ?.value;
          if (selectedTeamId) {
            platformTeam = await this.platformTeamsRepo.findByCondition({
              where: {
                uid: selectedTeamId,
                installation: { id: installation.id },
              },
            });

            if (!platformTeam) {
              throw new Error(
                'This channel was not found mapped to a team on platform.',
              );
            }
          } else {
            throw new Error(
              'This channel was not found mapped to a team on platform.',
            );
          }
        } else {
          platformTeam = teamChannelMap.platformTeam;
        }

        const ticketData = this.getFormValues(view);

        // Create a ticket using TicketCreationHelper
        const ticketPayload = {
          title: ticketData.title,
          requestorEmail: ticketData.requestorEmail,
          teamId: platformTeam.uid,
          text: ticketData.description,
          description: ticketData.description,
          metadata: {
            slack: {
              channel: privateMetadata.channelId,
              ts: 'SLASH_TICKET',
              user: body.user.id,
            },
          },
        };

        const ticket = await this.ticketCreationHelper.createTicketWithMetadata(
          installation,
          ticketPayload,
        );

        // If the response URL is present, send a message to the user
        if (privateMetadata.responseUrl) {
          await fetch(privateMetadata.responseUrl, {
            method: 'POST' as const,
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              response_type: 'ephemeral',
              delete_original: true,
            }),
          });
        }

        // If the should link slack message flag is present, link the slack message
        if (privateMetadata.shouldLinkSlackMessage && privateMetadata.threadTs) {
          await this.ticketCreationHelper.postConversationThreadToPlatform(
            installation,
            ticket,
            {
              channelId: privateMetadata.channelId,
              messageTs: privateMetadata.threadTs,
              userId: body.user.id,
              shouldProcessReactions: true,
              shouldSendConfirmation: true,
            },
          );
        } else {
          // Send a message to the user to configure the channel
          await client.chat.postMessage({
            token: installation.botToken,
            channel: privateMetadata.channelId,
            text: `Ticket created successfully. Ticket ID: ${ticket.ticketId}`,
          });
        }
      }
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          `${LOG_SPAN} Error handling create ticket view submission`,
          error.stack,
        );
      } else {
        console.error(
          `${LOG_SPAN} Error handling create ticket view submission`,
          error,
        );
      }

      // Send a message to the user to configure the channel
      await client.chat.postMessage({
        token: installation.botToken,
        channel: body.user.id,
        text: 'An error occurred while creating a ticket. Please try again later or if the issue persists, contact support.',
      });
    }
  }



  /**
   * Get the form values from the view
   * @param view The view
   * @returns The form values
   */
  private getFormValues(view: SlackViewMiddlewareArgs['view']) {
    const values = view.state.values;

    // Get the form values
    const ticketData = {
      title: values.title_block.title_input.value,
      priority:
        values?.priority_block?.[
          CreateTicketsBlocksComposite.ACTION_IDS.PRIORITY
        ]?.selected_option?.value,
      requestorEmail: values.requestor_email_block.requestor_email_input.value,
      description: values.description_block.description_input.value,
    };

    return ticketData;
  }

  /**
   * Get the private metadata from the view
   * @param view The view
   * @returns The private metadata
   */
  private getPrivateMetadata(view: SlackViewMiddlewareArgs['view']) {
    try {
      return JSON.parse(view.private_metadata);
    } catch (parsingError) {
      if (parsingError instanceof Error) {
        this.logger.error(
          `${LOG_SPAN} Error parsing private metadata`,
          parsingError.stack,
        );
      } else {
        console.error(
          `${LOG_SPAN} Error parsing private metadata`,
          parsingError,
        );
      }

      throw parsingError;
    }
  }
}
